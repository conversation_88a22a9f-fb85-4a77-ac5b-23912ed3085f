-- Step 1: Get first normal balance transaction for each user
WITH first_balance_dates AS (
    SELECT
        user_id,
        MIN(created_at) as first_balance_date
    FROM balance_transactions
    WHERE deleted IS NULL
    GROUP BY user_id
),
first_normal_balance AS (
    SELECT
        fbd.user_id,
        fbd.first_balance_date,
        bt.amount as first_balance_amount,
        bt.current_amount as first_balance_current_amount
    FROM first_balance_dates fbd
    JOIN balance_transactions bt ON bt.user_id = fbd.user_id
        AND bt.created_at = fbd.first_balance_date
        AND bt.deleted IS NULL
),
first_balance_matches AS (
    -- Find beneficiary transactions that match with first balance transaction
    SELECT DISTINCT bt.id as beneficiary_transaction_id
    FROM project_expenses pe
    JOIN beneficiary_transactions bt ON bt.id = pe.beneficiary_transaction_id
    JOIN first_normal_balance fnb ON pe.created_by = fnb.user_id
    JOIN balance_transactions bts ON bts.user_id = pe.created_by
        AND bts.created_at = fnb.first_balance_date
    WHERE ABS(EXTRACT(EPOCH FROM (pe.created_at - fnb.first_balance_date)) * 1000) <= 250
    AND -bt.amount = bts.amount
    AND bts.deleted IS NULL
),

-- Step 2: Get historical beneficiary transactions that need balance transactions
historical_beneficiary_transactions AS (
    SELECT
        bt.id as beneficiary_transaction_id,
        pe.created_by as user_id,
        pe.organization_id,
        pe.created_at,
        pe.updated_at,
        pe.updated_by,
        -bt.amount as amount,  -- Negative because it's a deduction
        p.name as project_name,
        bt.description as transaction_description,
        ROW_NUMBER() OVER (PARTITION BY pe.created_by ORDER BY pe.created_at) as rn
    FROM project_expenses pe
    JOIN projects p ON p.id = pe.project_id
    JOIN beneficiary_transactions bt ON bt.id = pe.beneficiary_transaction_id
    JOIN first_normal_balance fnb ON pe.created_by = fnb.user_id
    WHERE pe.created_at < fnb.first_balance_date
    AND pe.deleted IS NULL
    AND bt.deleted IS NULL
    AND p.deleted IS NULL
    AND bt.id NOT IN (SELECT beneficiary_transaction_id FROM first_balance_matches)
),

-- Step 3: Calculate running totals for historical transactions
historical_balance_transactions AS (
    SELECT
        h1.organization_id,
        h1.user_id,
        h1.user_id as created_by,   -- Fix: explicitly set created_by
        h1.user_id as updated_by,   -- Fix: explicitly set updated_by
        h1.created_at,
        h1.updated_at,
        h1.amount,
        (SELECT SUM(h2.amount)
         FROM historical_beneficiary_transactions h2
         WHERE h2.user_id = h1.user_id
         AND h2.rn <= h1.rn) as current_amount,
        CONCAT(
            'إضافة مصروفات: ',
            h1.project_name, E'\n',
            h1.transaction_description, E'\n',
            'المتاح حاليا: ',
            (SELECT SUM(h2.amount)
             FROM historical_beneficiary_transactions h2
             WHERE h2.user_id = h1.user_id
             AND h2.rn <= h1.rn)::text
        ) as description,
        1 as version
    FROM historical_beneficiary_transactions h1
),

-- Step 4: Get the last historical balance for each user
last_historical_balance AS (
    -- Get the last historical balance for each user using row_number to ensure we get the correct last record
    SELECT
        user_id,
        organization_id,
        created_at as last_historical_date,
        current_amount as last_historical_amount
    FROM (
        SELECT
            user_id,
            organization_id,
            created_at,
            current_amount,
            ROW_NUMBER() OVER (PARTITION BY user_id ORDER BY created_at DESC) as rn
        FROM historical_balance_transactions
    ) last_records
    WHERE rn = 1
),

-- Step 5: Calculate settlement transactions to bridge the gap
settlement_transactions AS (
    SELECT
        COALESCE(lhb.organization_id, u.organization_id) as organization_id,
        fnb.user_id,
        fnb.user_id as created_by,
        fnb.user_id as updated_by,
        fnb.first_balance_date as created_at,
        fnb.first_balance_date as updated_at,
        -- Calculate the amount needed to bridge from last historical to first normal
        CASE
            WHEN lhb.last_historical_amount IS NULL
            THEN fnb.first_balance_current_amount - fnb.first_balance_amount  -- Initial balance for new users
            ELSE (fnb.first_balance_current_amount - fnb.first_balance_amount) - lhb.last_historical_amount  -- Bridge the gap
        END as amount,
        fnb.first_balance_current_amount - fnb.first_balance_amount as current_amount,
        CASE
            WHEN lhb.last_historical_amount IS NULL
            THEN CONCAT(
                'تصفية عهدة: مبلغ ابتدائي - المتاح حاليا: ',
                (fnb.first_balance_current_amount - fnb.first_balance_amount)::text
            )
            ELSE CONCAT(
                'تصفية عهدة: تسوية الرصيد من ',
                lhb.last_historical_amount::text,
                ' الى ',
                (fnb.first_balance_current_amount - fnb.first_balance_amount)::text,
                ' - المتاح حاليا: ',
                (fnb.first_balance_current_amount - fnb.first_balance_amount)::text
            )
        END as description,
        1 as version
    FROM first_normal_balance fnb
    JOIN users u ON u.id = fnb.user_id AND u.deleted IS NULL
    LEFT JOIN last_historical_balance lhb ON lhb.user_id = fnb.user_id
    WHERE (
        lhb.last_historical_amount IS NULL  -- New users need initial balance
        OR (fnb.first_balance_current_amount - fnb.first_balance_amount) != lhb.last_historical_amount  -- Existing users need adjustment
    )
)

-- Final step: Insert all transactions in order
INSERT INTO balance_transactions (
    organization_id,
    user_id,
    created_by,
    updated_by,
    created_at,
    updated_at,
    amount,
    current_amount,
    description,
    version
)
SELECT
    organization_id,
    user_id,
    created_by,
    updated_by,
    created_at,
    updated_at,
    amount,
    current_amount,
    description,
    version
FROM historical_balance_transactions

UNION ALL

SELECT
    organization_id,
    user_id,
    created_by,
    updated_by,
    created_at,
    updated_at,
    amount,
    current_amount,
    description,
    version
FROM settlement_transactions
ORDER BY user_id, created_at;
































-- Add new columns
ALTER TABLE balance_transactions ADD COLUMN transaction_tag VARCHAR(255) NOT NULL DEFAULT 'UNKNOWN';
ALTER TABLE balance_transactions ADD COLUMN reference VARCHAR(255);

-- Update transaction tags based on description patterns
UPDATE balance_transactions
SET transaction_tag = CASE
    WHEN description LIKE 'إضافة مصروفات:%' THEN 'PROJECT_EXPENSE_ADD'
    WHEN description LIKE 'تحديث مصروفات:%' THEN 'PROJECT_EXPENSE_MODIFY'
    WHEN description LIKE 'حذف مصروفات:%' THEN 'PROJECT_EXPENSE_DELETE'
    WHEN description LIKE 'إضافة عهدة%' THEN 'BALANCE_ADD'
    WHEN description LIKE 'سحب عهدة%' THEN 'BALANCE_WITHDRAW'
    WHEN description LIKE 'تصفية عهدة%' THEN 'BALANCE_SETTLEMENT'
    ELSE transaction_tag
END;

-- Update reference with project id for project-related transactions
WITH project_references AS (
    SELECT bt.id as transaction_id, p.id as project_id
    FROM balance_transactions bt
    JOIN projects p ON bt.description LIKE CONCAT('إضافة مصروفات: ', p.name, '%')
        OR bt.description LIKE CONCAT('تحديث مصروفات: ', p.name, '%')
        OR bt.description LIKE CONCAT('حذف مصروفات: ', p.name, '%')
)
UPDATE balance_transactions bt
SET reference = pr.project_id::TEXT
FROM project_references pr
WHERE bt.id = pr.transaction_id;

-- Remove the default value constraint
ALTER TABLE balance_transactions ALTER COLUMN transaction_tag DROP DEFAULT;














-- Add accumulator columns to beneficiaries table
ALTER TABLE beneficiaries
    ADD COLUMN balance_accumulator DECIMAL(30,2) DEFAULT 0 NOT NULL,
    ADD COLUMN paid_accumulator DECIMAL(30,2) DEFAULT 0 NOT NULL;

-- Add accumulator columns to customers table
ALTER TABLE customers
    ADD COLUMN balance_accumulator DECIMAL(30,2) DEFAULT 0 NOT NULL,
    ADD COLUMN paid_accumulator DECIMAL(30,2) DEFAULT 0 NOT NULL;

-- Initialize beneficiary accumulators from existing transactions
UPDATE beneficiaries b
SET balance_accumulator = COALESCE((
    SELECT SUM(amount)
    FROM beneficiary_transactions
    WHERE beneficiary_id = b.id
      AND deleted IS NULL
), 0),
    paid_accumulator = COALESCE((
    SELECT SUM(amount_paid)
    FROM beneficiary_transactions
    WHERE beneficiary_id = b.id
      AND deleted IS NULL
), 0);

-- Initialize customer accumulators from existing transactions
UPDATE customers c
SET balance_accumulator = COALESCE((
    SELECT SUM(amount)
    FROM customer_transactions
    WHERE customer_id = c.id
      AND deleted IS NULL
), 0),
    paid_accumulator = COALESCE((
    SELECT SUM(amount_paid)
    FROM customer_transactions
    WHERE customer_id = c.id
      AND deleted IS NULL
), 0);


-- Remove default values from beneficiaries table accumulators
ALTER TABLE beneficiaries
    ALTER COLUMN balance_accumulator DROP DEFAULT,
    ALTER COLUMN paid_accumulator DROP DEFAULT;

-- Remove default values from customers table accumulators
ALTER TABLE customers
    ALTER COLUMN balance_accumulator DROP DEFAULT,
    ALTER COLUMN paid_accumulator DROP DEFAULT;



















-- Add new columns to organizations table
ALTER TABLE organizations
ADD COLUMN total_expenses DECIMAL(19,2),
ADD COLUMN total_paid_expenses DECIMAL(19,2),
ADD COLUMN total_incomes DECIMAL(19,2),
ADD COLUMN total_paid_incomes DECIMAL(19,2);

-- Initialize the columns with existing data
UPDATE organizations o
SET total_expenses = (
    SELECT COALESCE(SUM(bt.amount), 0)
    FROM beneficiary_transactions bt
    WHERE bt.organization_id = o.id
    AND bt.deleted IS NULL
),
total_paid_expenses = (
    SELECT COALESCE(SUM(bt.amount_paid), 0)
    FROM beneficiary_transactions bt
    WHERE bt.organization_id = o.id
    AND bt.deleted IS NULL
),
total_incomes = (
    SELECT COALESCE(SUM(bt.amount), 0)
    FROM beneficiary_transactions bt
    WHERE bt.organization_id = o.id
    AND bt.deleted IS NULL
),
total_paid_incomes = (
    SELECT COALESCE(SUM(bt.amount_paid), 0)
    FROM beneficiary_transactions bt
    WHERE bt.organization_id = o.id
    AND bt.deleted IS NULL
);

-- After setting the values, make the columns NOT NULL
ALTER TABLE organizations
ALTER COLUMN total_expenses SET NOT NULL,
ALTER COLUMN total_paid_expenses SET NOT NULL,
ALTER COLUMN total_incomes SET NOT NULL,
ALTER COLUMN total_paid_incomes SET NOT NULL;
















-- Add available_balance column to organizations table
ALTER TABLE organizations
ADD COLUMN available_balance DECIMAL(30,2);

-- Initialize available_balance with the formula: (capital + incomesPaid) - (expensesPaid + sum of users balances)
UPDATE organizations o
SET available_balance = (
    -- Calculate: (capital + total_paid_incomes) - (total_paid_expenses + sum_of_user_balances)
    o.capital +
    COALESCE((
        SELECT SUM(p.total_paid_incomes)
        FROM projects p
        WHERE p.organization_id = o.id
        AND p.deleted IS NULL
    ), 0) -
    COALESCE((
        SELECT SUM(p.total_paid_expenses)
        FROM projects p
        WHERE p.organization_id = o.id
        AND p.deleted IS NULL
    ), 0) -
    COALESCE((
        SELECT SUM(u.balance)
        FROM users u
        WHERE u.organization_id = o.id
        AND u.deleted IS NULL
    ), 0)
);

-- Set the column as NOT NULL after initialization
ALTER TABLE organizations
ALTER COLUMN available_balance SET NOT NULL;

-- Add a comment to document the formula
COMMENT ON COLUMN organizations.available_balance IS 'Available balance calculated as: (capital + incomes_paid) - (expenses_paid + sum_of_users_balances)';
