package com.project.management.money

import com.project.management.beneficiaries.mappers.toBeneficiaryTransaction
import com.project.management.beneficiaries.mappers.toEntity
import com.project.management.beneficiaries.services.BeneficiaryTransactionMutateService
import com.project.management.beneficiaries.validators.BeneficiaryValidator
import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.common.exceptions.BusinessException
import com.project.management.projects.models.ProjectExpense
import com.project.management.projects.models.ProjectExpenseEntity
import com.project.management.projects.repositories.ProjectExpenseMutateRepository
import com.project.management.projects.requests.PostRequestProjectExpense
import com.project.management.projects.services.ProjectService
import com.project.management.projects.validators.ProjectExpenseValidator
import com.project.management.projects.validators.ProjectValidator
import com.project.management.terms.validators.TermValidator
import com.project.management.terms.validators.TermsGroupValidator
import com.project.management.users.models.User
import com.project.management.users.services.UsersService
import org.springframework.transaction.annotation.Transactional

class AddOutMoneyTransactionUseCase(
    private val projectExpenseRepository: ProjectExpenseMutateRepository,
    private val currentUser: CurrentUserConfig,
    private val projectValidator: ProjectValidator,
    private val termsGroupValidator: TermsGroupValidator,
    private val termValidator: TermValidator,
    private val beneficiaryValidator: BeneficiaryValidator,
    private val projectExpenseValidator: ProjectExpenseValidator,
    private val beneficiaryTransactionQueryService: BeneficiaryTransactionMutateService,
    private val projectService: ProjectService,
    private val usersService: UsersService,
) {

    @Transactional
    fun create(
        projectExpenseRequestDto: PostRequestProjectExpense,
        projectId: Long
    ): ProjectExpense {
        val user = currentUser.getCurrentUser()
        projectExpenseRequestDto.validate(user)

        val project = projectValidator.validateExistsByIdAndOrganizationId(
            projectId = projectId,
            organizationId = user.organizationId
        )

        val projectExpenseEntity = createOutTransaction(
            projectExpenseRequestDto = projectExpenseRequestDto,
            projectId = projectId,
            user = user
        )

        usersService.decreaseBalance(
            user.id!!,
            projectExpenseRequestDto.amount,
            description = "إضافة مصروفات: ${project.name}\n${projectExpenseRequestDto.description}",
        )

        projectService.modifyExpenses(
            projectId = projectId,
            amount = projectExpenseRequestDto.amount.toBigDecimal(),
            paid = projectExpenseRequestDto.amountPaid.toBigDecimal()
        )

        return projectExpenseValidator.validateExistsByIdAndOrganization(
            projectExpenseId = projectExpenseEntity.id!!,
            organizationId = user.organizationId
        )
    }

    private fun createOutTransaction(
        projectExpenseRequestDto: PostRequestProjectExpense,
        projectId: Long,
        user: User
    ): ProjectExpenseEntity {
        val outTransaction = beneficiaryTransactionQueryService.create(
            transaction = projectExpenseRequestDto.toBeneficiaryTransaction(projectId),
            beneficiaryId = projectExpenseRequestDto.beneficiaryId
        )
        val projectExpense = projectExpenseRequestDto.toEntity(
            projectId = projectId,
            beneficiaryTransaction = outTransaction,
            user = user,
            version = 1
        )

        return projectExpenseRepository.save(projectExpense)
    }

    private fun PostRequestProjectExpense.validate(user: User) {
        val termsGroup = termsGroupValidator.validateTermsGroupExistsByIdAndOrganizationId(
            termsGroupId = termsGroupId,
            organizationId = user.organizationId
        )
        val term = termValidator.validateTermExistsByIdAndOrganizationId(
            termId = termId,
            organizationId = user.organizationId
        )

        if (term.termsGroupId != termsGroup.id) {
            throw BusinessException.ConflictException(message = "Term and TermsGroup do not match.")
        }

        beneficiaryValidator.validateBeneficiaryExistsByIdAndOrganizationId(
            beneficiaryId = beneficiaryId,
            organizationId = user.organizationId
        )
    }
}