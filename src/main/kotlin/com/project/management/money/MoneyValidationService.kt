package com.project.management.money

import com.project.management.beneficiaries.validators.BeneficiaryTransactionValidator
import com.project.management.beneficiaries.validators.BeneficiaryValidator
import com.project.management.organization.services.CapitalTransactionsService
import com.project.management.organization.validators.OrganizationValidator
import com.project.management.projects.validators.ProjectExpenseValidator
import com.project.management.projects.validators.ProjectValidator
import com.project.management.users.services.BalanceTransactionService
import org.springframework.stereotype.Service

@Service
class MoneyValidationService(
    private val beneficiaries: BeneficiaryValidator,
    private val beneficiaryTransactionValidator: BeneficiaryTransactionValidator,
    private val projectExpenseValidator: ProjectExpenseValidator,
    private val projectValidator: ProjectValidator,
    private val usersTransactions: BalanceTransactionService,
    private val capitalTransactionsService: CapitalTransactionsService,
    private val organizationValidator: OrganizationValidator,
) {

    fun validateExpenses(organizationId: Long) {
        val userExpenses = usersTransactions
            .getAll()
            .filter { it.transactionTag.isProjectExpense() }
            .sumOf { it.amount }

        val projects = projectValidator.findAllByOrganizationId(organizationId)
        val projectsAmount = projects.sumOf { it.totalExpenses }
        val projectsPaid = projects.sumOf { it.totalPaidExpenses }

        val projectTransactions = projectExpenseValidator
            .findAllByOrganizationId(organizationId)
        val projectsTransactionsAmount = projectTransactions.sumOf { it.beneficiaryTransaction.amount }
        val projectsTransactionsPaid = projectTransactions.sumOf { it.beneficiaryTransaction.amountPaid }

        val beneficiary = beneficiaries.findAllByOrganizationId(organizationId)
        val beneficiariesAmount = beneficiary.sumOf { it.balanceAccumulator }
        val beneficiariesPaid = beneficiary.sumOf { it.paidAccumulator }

        val beneficiaryTransactions = beneficiaryTransactionValidator.findAllByOrganizationId(organizationId)
        val beneficiaryTransactionsAmount = beneficiaryTransactions.sumOf { it.amount }
        val beneficiaryTransactionsPaid = beneficiaryTransactions.sumOf { it.amountPaid }

        if (userExpenses.negate() != projectsTransactionsPaid) {
            throw IllegalStateException("User expenses do not match project expenses paid: $userExpenses != $projectsTransactionsPaid")
        }

        if (projectsTransactionsAmount != beneficiaryTransactionsAmount) {
            throw IllegalStateException("Projects amount does not match beneficiaries amount: $projectsTransactionsAmount != $beneficiaryTransactionsAmount")
        }

        if (projectsTransactionsPaid != beneficiaryTransactionsPaid) {
            throw IllegalStateException("Projects paid does not match beneficiaries paid: $projectsTransactionsPaid != $beneficiaryTransactionsPaid")
        }

        if (projectsTransactionsAmount != projectsAmount) {
            throw IllegalStateException("Projects amount does not match project expenses amount: $projectsTransactionsAmount != $projectsAmount")
        }

        if (projectsTransactionsPaid != projectsPaid) {
            throw IllegalStateException("Projects paid does not match project expenses paid: $projectsTransactionsPaid != $projectsPaid")
        }

        if (projectsTransactionsAmount != beneficiariesAmount) {
            throw IllegalStateException("Projects amount does not match beneficiaries amount: $projectsTransactionsAmount != $beneficiariesAmount")
        }

        if (projectsTransactionsPaid != beneficiariesPaid) {
            throw IllegalStateException("Projects paid does not match beneficiaries paid: $projectsTransactionsPaid != $beneficiariesPaid")
        }
    }
}