package com.project.management.money

import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.organization.models.OrganizationEntity
import com.project.management.organization.repositories.OrganizationRepository
import com.project.management.organization.validators.OrganizationValidator
import com.project.management.users.repositories.UserRepository
import com.project.management.projects.repositories.ProjectRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal

@Service
class OrganizationMoneyService(
    private val organizationRepository: OrganizationRepository,
    private val userRepository: UserRepository,
    private val projectRepository: ProjectRepository,
    private val currentUser: CurrentUserConfig,
    private val organizationValidator: OrganizationValidator,
) {

    /**
     * Calculates the available balance for an organization using the formula:
     * (capital + incomesPaid) - (expensesPaid + sum of users balances)
     */
    @Transactional(readOnly = true)
    fun calculateAvailableBalance(organizationId: Long): BigDecimal {
        val organization = organizationValidator.validateOrganizationExistsByIdAndOrganizationId(organizationId)
        
        // Get all projects for this organization to sum up paid incomes and expenses
        val projects = projectRepository.findAllByOrganizationId(organizationId)
        val totalPaidIncomes = projects.sumOf { it.totalPaidIncomes }
        val totalPaidExpenses = projects.sumOf { it.totalPaidExpenses }
        
        // Get sum of all user balances in this organization
        val users = userRepository.findAllByOrganizationId(organizationId)
        val totalUserBalances = users.sumOf { it.balance }
        
        // Calculate available balance: (capital + incomesPaid) - (expensesPaid + sum of users balances)
        return (organization.capital + totalPaidIncomes) - (totalPaidExpenses + totalUserBalances)
    }

    /**
     * Gets the current available balance for the current user's organization
     */
    @Transactional(readOnly = true)
    fun getCurrentOrganizationAvailableBalance(): BigDecimal {
        val user = currentUser.getCurrentUser()
        return calculateAvailableBalance(user.organizationId)
    }

    /**
     * Checks if the organization has sufficient available balance for a given amount
     */
    @Transactional(readOnly = true)
    fun hasSufficientBalance(organizationId: Long, amount: BigDecimal): Boolean {
        val availableBalance = calculateAvailableBalance(organizationId)
        return availableBalance >= amount
    }

    /**
     * Checks if the current user's organization has sufficient available balance for a given amount
     */
    @Transactional(readOnly = true)
    fun currentOrganizationHasSufficientBalance(amount: BigDecimal): Boolean {
        val user = currentUser.getCurrentUser()
        return hasSufficientBalance(user.organizationId, amount)
    }

    /**
     * Gets organization financial summary including available balance
     */
    @Transactional(readOnly = true)
    fun getOrganizationFinancialSummary(organizationId: Long): OrganizationFinancialSummary {
        val organization = organizationValidator.validateOrganizationExistsByIdAndOrganizationId(organizationId)
        
        // Get all projects for this organization
        val projects = projectRepository.findAllByOrganizationId(organizationId)
        val totalPaidIncomes = projects.sumOf { it.totalPaidIncomes }
        val totalPaidExpenses = projects.sumOf { it.totalPaidExpenses }
        val totalIncomes = projects.sumOf { it.totalIncomes }
        val totalExpenses = projects.sumOf { it.totalExpenses }
        
        // Get sum of all user balances
        val users = userRepository.findAllByOrganizationId(organizationId)
        val totalUserBalances = users.sumOf { it.balance }
        
        val availableBalance = (organization.capital + totalPaidIncomes) - (totalPaidExpenses + totalUserBalances)
        
        return OrganizationFinancialSummary(
            organizationId = organizationId,
            capital = organization.capital,
            totalIncomes = totalIncomes,
            totalPaidIncomes = totalPaidIncomes,
            totalExpenses = totalExpenses,
            totalPaidExpenses = totalPaidExpenses,
            totalUserBalances = totalUserBalances,
            availableBalance = availableBalance
        )
    }

    /**
     * Gets financial summary for the current user's organization
     */
    @Transactional(readOnly = true)
    fun getCurrentOrganizationFinancialSummary(): OrganizationFinancialSummary {
        val user = currentUser.getCurrentUser()
        return getOrganizationFinancialSummary(user.organizationId)
    }

    /**
     * Increases organization capital
     */
    @Transactional
    fun increaseCapital(organizationId: Long, amount: BigDecimal, description: String): OrganizationEntity {
        val user = currentUser.getCurrentUser()
        val organization = organizationValidator.validateOrganizationExistsByIdAndOrganizationId(organizationId)
        
        val updatedOrganization = organization.copy(
            capital = organization.capital.add(amount),
            updatedBy = user.id
        )
        
        return organizationRepository.save(updatedOrganization)
    }

    /**
     * Decreases organization capital
     */
    @Transactional
    fun decreaseCapital(organizationId: Long, amount: BigDecimal, description: String): OrganizationEntity {
        val user = currentUser.getCurrentUser()
        val organization = organizationValidator.validateOrganizationExistsByIdAndOrganizationId(organizationId)
        
        val updatedOrganization = organization.copy(
            capital = organization.capital.subtract(amount),
            updatedBy = user.id
        )
        
        return organizationRepository.save(updatedOrganization)
    }
}

/**
 * Data class representing organization financial summary
 */
data class OrganizationFinancialSummary(
    val organizationId: Long,
    val capital: BigDecimal,
    val totalIncomes: BigDecimal,
    val totalPaidIncomes: BigDecimal,
    val totalExpenses: BigDecimal,
    val totalPaidExpenses: BigDecimal,
    val totalUserBalances: BigDecimal,
    val availableBalance: BigDecimal
)
