package com.project.management.money.organization

import com.project.management.beneficiaries.mappers.toBeneficiaryTransaction
import com.project.management.beneficiaries.mappers.toEntity
import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.beneficiaries.models.BeneficiaryEntity
import com.project.management.beneficiaries.models.BeneficiaryTransactionEntity
import com.project.management.organization.models.OrganizationEntity
import com.project.management.organization.repositories.OrganizationRepository
import com.project.management.organization.validators.OrganizationValidator
import com.project.management.projects.requests.PostRequestProjectExpense
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal

@Service
class OrganizationMoneyMutateService(
    private val organizations: OrganizationRepository,
    private val currentUser: CurrentUserConfig,
    private val organizationValidator: OrganizationValidator,
) {

    fun increaseExpenses(
        amount: BigDecimal,
        amountPaid: BigDecimal
    ): OrganizationEntity {
        val currentUser = currentUser.getCurrentUser()
        var organization = organizationValidator.validateOrganizationExistsByIdAndOrganizationId(
            organizationId = currentUser.organizationId
        )
        val updated = organization.copy(
            totalExpenses = organization.totalExpenses.add(amount),
            totalPaidExpenses = organization.totalPaidExpenses.add(amountPaid),
            updatedBy = currentUser.id
        )

        return organizations.save(updated)
    }

    fun decreaseExpenses(
        amount: BigDecimal,
        amountPaid: BigDecimal
    ): OrganizationEntity {
        val currentUser = currentUser.getCurrentUser()
        val organization = organizationValidator.validateOrganizationExistsByIdAndOrganizationId(
            organizationId = currentUser.organizationId
        )
        val updated = organization.copy(
            totalExpenses = organization.totalExpenses.minus(amount),
            totalPaidExpenses = organization.totalPaidExpenses.minus(amountPaid),
            updatedBy = currentUser.id
        )

        return organizations.save(updated)
    }

    fun modifyExpenses(
        oldAmount: BigDecimal,
        oldAmountPaid: BigDecimal,
        newAmount: BigDecimal,
        newAmountPaid: BigDecimal
    ): OrganizationEntity {
        val amount = oldAmount.negate() + newAmount
        val amountPaid = oldAmountPaid.negate() + newAmountPaid

        return increaseExpenses(amount = amount, amountPaid = amountPaid)
    }

    fun increaseIncomes(
        amount: BigDecimal,
        amountPaid: BigDecimal
    ): OrganizationEntity {
        val currentUser = currentUser.getCurrentUser()
        var organization = organizationValidator.validateOrganizationExistsByIdAndOrganizationId(
            organizationId = currentUser.organizationId
        )
        val updated = organization.copy(
            totalIncomes = organization.totalIncomes.add(amount),
            totalPaidIncomes = organization.totalPaidIncomes.add(amountPaid),
            updatedBy = currentUser.id
        )

        return organizations.save(updated)
    }

    fun decreaseIncomes(
        amount: BigDecimal,
        amountPaid: BigDecimal
    ): OrganizationEntity {
        val currentUser = currentUser.getCurrentUser()
        var organization = organizationValidator.validateOrganizationExistsByIdAndOrganizationId(
            organizationId = currentUser.organizationId
        )
        val updated = organization.copy(
            totalIncomes = organization.totalIncomes.minus(amount),
            totalPaidIncomes = organization.totalPaidIncomes.minus(amountPaid),
            updatedBy = currentUser.id
        )

        return organizations.save(updated)
    }

    fun modifyIncomes(
        oldAmount: BigDecimal,
        oldAmountPaid: BigDecimal,
        newAmount: BigDecimal,
        newAmountPaid: BigDecimal
    ): OrganizationEntity {
        val amount = oldAmount.negate() + newAmount
        val amountPaid = oldAmountPaid.negate() + newAmountPaid

        return increaseIncomes(amount = amount, amountPaid = amountPaid)
    }
}

