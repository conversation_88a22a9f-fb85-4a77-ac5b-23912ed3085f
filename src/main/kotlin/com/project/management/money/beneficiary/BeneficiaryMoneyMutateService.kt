package com.project.management.money.beneficiary

import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.beneficiaries.models.BeneficiaryEntity
import com.project.management.beneficiaries.repositories.BeneficiaryRepository
import com.project.management.beneficiaries.validators.BeneficiaryValidator
import com.project.management.projects.models.ProjectEntity
import com.project.management.projects.models.ProjectExpense
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.math.BigDecimal

@Service
class BeneficiaryMoneyMutateService(
    private val beneficiaryRepository: BeneficiaryRepository,
    private val currentUser: CurrentUserConfig,
    private val beneficiaryValidator: BeneficiaryValidator,
) {

    @Transactional
    fun projectExpenseAdd(beneficiaryId: Long, expense: ProjectExpense): BeneficiaryEntity {
        return increaseAccumulators(
            beneficiaryId = beneficiaryId,
            amount = expense.beneficiaryTransaction.amount,
            amountPaid = expense.beneficiaryTransaction.amountPaid
        )
    }

    @Transactional
    fun projectExpenseDelete(beneficiaryId: Long, amount: BigDecimal, amountPaid: BigDecimal): BeneficiaryEntity {
        return decreaseAccumulators(
            beneficiaryId = beneficiaryId,
            amount = amount,
            amountPaid = amountPaid
        )
    }

    @Transactional
    fun projectExpenseModify(
        beneficiaryId: Long,
        oldAmount: BigDecimal,
        oldAmountPaid: BigDecimal,
        newAmount: BigDecimal,
        newAmountPaid: BigDecimal
    ): BeneficiaryEntity {
        val amount = oldAmount.negate() + newAmount
        val amountPaid = oldAmountPaid.negate() + newAmountPaid

        return increaseAccumulators(
            beneficiaryId = beneficiaryId,
            amount = amount,
            amountPaid = amountPaid
        )
    }

    private fun increaseAccumulators(
        beneficiaryId: Long,
        amount: BigDecimal,
        amountPaid: BigDecimal
    ): BeneficiaryEntity {
        val currentUser = currentUser.getCurrentUser()
        var beneficiary = beneficiaryValidator.validateBeneficiaryExistsByIdAndOrganizationId(
            beneficiaryId = beneficiaryId,
            organizationId = currentUser.organizationId
        )

        beneficiary.balanceAccumulator = beneficiary.balanceAccumulator.plus(amount)
        beneficiary.paidAccumulator = beneficiary.paidAccumulator.plus(amountPaid)
        beneficiary.updatedBy = currentUser.id
        beneficiary = beneficiaryRepository.save(beneficiary)

        return beneficiary
    }

    private fun decreaseAccumulators(
        beneficiaryId: Long,
        amount: BigDecimal,
        amountPaid: BigDecimal
    ): BeneficiaryEntity {
        val currentUser = currentUser.getCurrentUser()
        var beneficiary = beneficiaryValidator.validateBeneficiaryExistsByIdAndOrganizationId(
            beneficiaryId = beneficiaryId,
            organizationId = currentUser.organizationId
        )

        beneficiary.balanceAccumulator = beneficiary.balanceAccumulator.minus(amount)
        beneficiary.paidAccumulator = beneficiary.paidAccumulator.minus(amountPaid)
        beneficiary.updatedBy = currentUser.id
        beneficiary = beneficiaryRepository.save(beneficiary)

        return beneficiary
    }
}

