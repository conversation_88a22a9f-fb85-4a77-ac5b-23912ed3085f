package com.project.management.beneficiaries.services

import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.beneficiaries.requests.PostRequestBeneficiaryTransaction
import com.project.management.beneficiaries.models.BeneficiaryTransactionEntity
import com.project.management.beneficiaries.requests.PatchRequestBeneficiaryTransaction
import com.project.management.projects.requests.PatchRequestProjectExpenseAmount
import com.project.management.beneficiaries.models.BeneficiaryTransaction
import com.project.management.beneficiaries.models.toEntity
import com.project.management.beneficiaries.validators.BeneficiaryTransactionValidator
import com.project.management.beneficiaries.validators.BeneficiaryValidator
import com.project.management.money.beneficiary.BeneficiaryTransactionMoneyService
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.ZonedDateTime

@Service
class BeneficiaryTransactionQueryService(
    private val currentUser: CurrentUserConfig,
    private val beneficiaryValidator: BeneficiaryValidator,
    private val validator: BeneficiaryTransactionValidator
) {

    fun getAll(): List<BeneficiaryTransaction> {
        val user = currentUser.getCurrentUser()
        val beneficiaryTransactions = validator.findAllByOrganizationId(user.organizationId)
        return beneficiaryTransactions
    }

    fun getAllByBeneficiaryId(beneficiaryId: Long): List<BeneficiaryTransaction> {
        val user = currentUser.getCurrentUser()
        val beneficiary = beneficiaryValidator.validateBeneficiaryExistsByIdAndOrganizationId(
            beneficiaryId = beneficiaryId,
            organizationId = user.organizationId
        )
        val beneficiaryTransactions = validator.findAllByOrganizationIdAndBeneficiaryId(
            organizationId = user.organizationId,
            beneficiaryId = beneficiary.id!!
        )
        return beneficiaryTransactions
    }
}

@Service
class BeneficiaryTransactionMutateService(
    private val beneficiaryTransactionMoneyService: BeneficiaryTransactionMoneyService,
) {
    @Transactional
    fun create(
        transaction: PostRequestBeneficiaryTransaction,
        beneficiaryId: Long
    ): BeneficiaryTransactionEntity {
        return beneficiaryTransactionMoneyService.create(transaction, beneficiaryId)
    }

    @Transactional
    fun updateAmount(
        request: PatchRequestProjectExpenseAmount,
        beneficiaryTransactionId: Long
    ): BeneficiaryTransactionEntity {
        return beneficiaryTransactionMoneyService.updateAmount(request, beneficiaryTransactionId)
    }

    @Transactional
    fun updateTransaction(
        request: PatchRequestBeneficiaryTransaction,
        beneficiaryTransactionId: Long
    ): BeneficiaryTransactionEntity {
        val loggedInUser = currentUser.getCurrentUser()
        val transaction = request.validate(beneficiaryTransactionId, loggedInUser).toEntity()

        val updatedTransaction = transaction.copy(
            description = request.description ?: transaction.description,
            transactionDate = request.transactionDate?.let { ZonedDateTime.parse(it) } ?: transaction.transactionDate,
            updatedBy = loggedInUser.id!!
        )

        return beneficiaryTransactionsRepository.save(updatedTransaction)
    }

    @Transactional
    fun delete(beneficiaryTransactionId: Long) {
        beneficiaryTransactionMoneyService.delete(beneficiaryTransactionId)
    }
}