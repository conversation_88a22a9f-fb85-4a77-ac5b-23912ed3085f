package com.project.management.beneficiaries.mappers

import com.project.management.common.annotation.MappingOrganizationEntity
import com.project.management.beneficiaries.requests.PostRequestBeneficiaryTransaction
import com.project.management.beneficiaries.models.BeneficiaryTransactionEntity
import com.project.management.projects.models.ProjectExpenseEntity
import com.project.management.projects.requests.PostRequestProjectExpense
import com.project.management.users.models.User
import org.mapstruct.Mapper
import org.mapstruct.Mapping
import org.mapstruct.ReportingPolicy
import java.time.ZonedDateTime

fun PostRequestBeneficiaryTransaction.toEntity(
    beneficiaryId: Long,
    user: User,
    version: Long
): BeneficiaryTransactionEntity {
    return BeneficiaryTransactionEntity(
        organizationId = user.organizationId,
        amount = amount.toBigDecimal(),
        amountPaid = amountPaid.toBigDecimal(),
        description = description,
        beneficiaryId = beneficiaryId,
        projectId = projectId,
        transactionDate = ZonedDateTime.parse(transactionDate),
        createdBy = user.id,
        updatedBy = user.id,
        version = version
    )
}

fun PostRequestProjectExpense.toBeneficiaryTransaction(projectId: Long): PostRequestBeneficiaryTransaction {
    return PostRequestBeneficiaryTransaction(
        amount = amount,
        amountPaid = amountPaid,
        description = description,
        projectId = projectId,
        transactionDate = transactionDate
    )
}

internal fun PostRequestProjectExpense.toEntity(
    projectId: Long,
    beneficiaryTransaction: BeneficiaryTransactionEntity,
    user: User,
    version: Long
): ProjectExpenseEntity {
    return ProjectExpenseEntity(
        organizationId = user.organizationId,
        beneficiaryId = beneficiaryId,
        beneficiaryTransactionId = beneficiaryTransaction.id!!,
        termsGroupId = termsGroupId,
        termId = termId,
        projectId = projectId,
        createdBy = user.id,
        updatedBy = user.id,
        version = version
    )
}