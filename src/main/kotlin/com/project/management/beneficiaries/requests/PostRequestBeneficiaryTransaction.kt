package com.project.management.beneficiaries.requests

import com.project.management.beneficiaries.validators.BeneficiaryValidator
import com.project.management.common.exceptions.BusinessException
import com.project.management.common.utility.Injection
import com.project.management.projects.validators.ProjectValidator
import com.project.management.users.models.User
import org.springframework.beans.factory.annotation.Autowired

data class PostRequestBeneficiaryTransaction(
    val amount: Double,
    val amountPaid: Double = amount,
    val description: String,
    val projectId: Long,
    val transactionDate: String
) {
    fun validate(beneficiaryId: Long, user: User) {
        Injection.beneficiaryValidator.validateBeneficiaryExistsByIdAndOrganizationId(
            beneficiaryId = beneficiaryId,
            organizationId = user.organizationId
        )
        Injection.projectValidator.validateExistsByIdAndOrganizationId(
            projectId = projectId,
            organizationId = user.organizationId
        )

        if (amountPaid > amount) {
            throw BusinessException.BadRequestException(message = "Amount paid cannot be greater than amount.")
        }
    }
}