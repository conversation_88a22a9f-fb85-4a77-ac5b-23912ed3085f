package com.project.management.beneficiaries.requests

import com.project.management.beneficiaries.models.BeneficiaryTransaction
import com.project.management.beneficiaries.validators.BeneficiaryTransactionValidator
import com.project.management.common.exceptions.BusinessException
import com.project.management.common.utility.Injection
import com.project.management.users.models.User
import org.springframework.beans.factory.annotation.Autowired

data class PatchRequestBeneficiaryTransaction(
    val description: String? = null,
    val transactionDate: String? = null,
    val version: Long
) {

    fun validate(beneficiaryTransactionId: Long, user: User): BeneficiaryTransaction {
        val transaction = Injection.beneficiaryTransactionsValidator.validateExistsByIdAndOrganization(
            beneficiaryTransactionId = beneficiaryTransactionId,
            organizationId = user.organizationId
        )
        if (transaction.version != version) throw BusinessException.ConflictException()

        return transaction
    }
}