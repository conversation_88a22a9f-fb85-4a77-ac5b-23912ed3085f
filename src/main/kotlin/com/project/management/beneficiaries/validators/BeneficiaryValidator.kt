package com.project.management.beneficiaries.validators

import com.project.management.common.exceptions.BusinessException
import com.project.management.beneficiaries.models.BeneficiaryEntity
import com.project.management.beneficiaries.models.BeneficiaryTransaction
import com.project.management.beneficiaries.models.BeneficiaryTransactionEntity
import com.project.management.beneficiaries.repositories.BeneficiaryRepository
import com.project.management.beneficiaries.repositories.BeneficiaryTransactionQueryRepository
import org.springframework.stereotype.Component

@Component
class BeneficiaryValidator(
    private val beneficiaryRepository: BeneficiaryRepository
) {

    fun validateBeneficiaryExistsByIdAndOrganizationId(
        beneficiaryId: Long,
        organizationId: Long
    ): BeneficiaryEntity {
        return beneficiaryRepository.findByIdAndOrganizationId(beneficiaryId, organizationId)
            ?: throw BusinessException.NotFoundException("Beneficiary with id $beneficiaryId not found")
    }
}

@Component
class BeneficiaryTransactionValidator(
    private val beneficiaryTransactionQueryRepository: BeneficiaryTransactionQueryRepository
) {

    fun findAllByOrganizationId(organizationId: Long): List<BeneficiaryTransaction> {
        return beneficiaryTransactionQueryRepository.findAllByOrganizationId(organizationId)
    }

    fun findAllByOrganizationIdAndBeneficiaryId(organizationId: Long, beneficiaryId: Long): List<BeneficiaryTransaction> {
        return beneficiaryTransactionQueryRepository.findAllByOrganizationIdAndBeneficiaryId(organizationId, beneficiaryId)
    }

    fun validateExistsByIdAndOrganization(
        beneficiaryTransactionId: Long,
        organizationId: Long
    ): BeneficiaryTransaction {
        return beneficiaryTransactionQueryRepository.findByIdAndOrganizationId(
            beneficiaryTransactionId,
            organizationId
        ) ?: throw BusinessException.NotFoundException("Beneficiary transaction with id $beneficiaryTransactionId not found")
    }
}