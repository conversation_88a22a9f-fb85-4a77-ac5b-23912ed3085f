package com.project.management.analytics.models

import com.project.management.projects.models.ProjectExpense
import com.project.management.projects.models.ProjectExpenseEntity
import com.project.management.projects.models.ProjectIncomeEntity
import java.math.BigDecimal

data class HomeSummary(
    val incomesThisWeek: BigDecimal,
    val expensesThisWeek: BigDecimal,
    val incomesLastWeek: BigDecimal,
    val expensesLastWeek: BigDecimal,
    val largestIncomes: List<ProjectIncomeEntity>,
    val largestExpenses: List<ProjectExpense>
)