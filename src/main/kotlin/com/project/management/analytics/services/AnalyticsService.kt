package com.project.management.analytics.services

import com.project.management.analytics.models.HomeSummary
import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.projects.repositories.ProjectIncomeRepository
import com.project.management.projects.validators.ProjectExpenseValidator
import org.springframework.stereotype.Service

@Service
class AnalyticsService(
    private val expensesRepository: ProjectExpenseValidator,
    private val incomesRepository: ProjectIncomeRepository,
    private val currentUser: CurrentUserConfig,
) {

    fun getHomeSummary(): HomeSummary {
        val user = currentUser.getCurrentUser()
        val expensesLastWeek = expensesRepository
            .findAllByOrganizationIdCreatedAtLastWeek(user.organizationId)
        val expensesThisWeek = expensesRepository
            .findAllByOrganizationIdCreatedThisWeek(user.organizationId)

        val incomesLastWeek = incomesRepository
            .findAllByOrganizationIdCreatedAtLastWeek(user.organizationId)
        val incomesThisWeek = incomesRepository
            .findAllByOrganizationIdCreatedThisWeek(user.organizationId)

        return HomeSummary(
            incomesThisWeek = incomesThisWeek.sumOf { it.customerTransaction.amount },
            expensesThisWeek = expensesThisWeek.sumOf { it.beneficiaryTransaction.amount },
            incomesLastWeek = incomesLastWeek.sumOf { it.customerTransaction.amount },
            expensesLastWeek = expensesLastWeek.sumOf { it.beneficiaryTransaction.amount },
            largestIncomes = incomesThisWeek.sortedByDescending { it.customerTransaction.amount }.take(5),
            largestExpenses = expensesThisWeek.sortedByDescending { it.beneficiaryTransaction.amount }.take(5),
        )
    }
}