package com.project.management.users.models

enum class UserTransactionTag {
    PROJECT_EXPENSE_ADD,
    PROJECT_EXPENSE_MODIFY,
    PROJECT_EXPENSE_DELETE,

    BA<PERSON>NCE_ADD,
    BALANCE_WITHDRAW,
    <PERSON><PERSON><PERSON><PERSON>_MODIFY,
    BALANCE_SETTLEMENT;

    fun isProjectExpense(): <PERSON><PERSON><PERSON> {
        return this == PROJECT_EXPENSE_ADD || this == PROJECT_EXPENSE_MODIFY || this == PROJECT_EXPENSE_DELETE
    }
}
