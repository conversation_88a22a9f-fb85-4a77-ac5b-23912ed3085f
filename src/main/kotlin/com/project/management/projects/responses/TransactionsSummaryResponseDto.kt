package com.project.management.projects.responses

import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.node.JsonNodeFactory
import com.project.management.projects.models.ProjectExpense
import com.project.management.projects.models.ProjectExpenseEntity
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter

fun ProjectExpense.toResponseDto(): JsonNode {
    val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS").withZone(ZoneOffset.UTC)
    val root = JsonNodeFactory.instance.objectNode()

    root.put("id", id)
    root.put("version", version)
    root.put("createdAt", formatter.format(createdAt))


    val term = root.putObject("term")
    term.put("name", this.term.name)
    val termsGroup = term.putObject("termsGroup")
    termsGroup.put("name", this.termsGroup.name)

    val beneficiary = root.putObject("beneficiary")
    beneficiary.put("name", this.beneficiary.name)
    beneficiary.put("id", this.beneficiary.id)

    val transaction = root.putObject("beneficiaryTransaction")
    transaction.put("id", this.beneficiaryTransaction.id)
    transaction.put("amount", this.beneficiaryTransaction.amount)
    transaction.put("amountPaid", this.beneficiaryTransaction.amountPaid)
    transaction.put("description", this.beneficiaryTransaction.description)
    transaction.put("createdAt", formatter.format(this.beneficiaryTransaction.createdAt))

    val createdByDetails = transaction.putObject("createdByDetails")
    createdByDetails.put("name", this.beneficiaryTransaction.createdByDetails.name)

    return root
}