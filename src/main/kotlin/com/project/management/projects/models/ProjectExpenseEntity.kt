package com.project.management.projects.models

import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.PreUpdate
import jakarta.persistence.Table
import org.hibernate.annotations.SQLRestriction
import java.time.ZoneOffset
import java.time.ZonedDateTime

@SQLRestriction("deleted IS NULL")
@Entity
@Table(name = "project_expenses")
data class ProjectExpenseEntity(
    val organizationId: Long,

    @Column(name = "beneficiary_id")
    val beneficiaryId: Long,
    @Column(name = "beneficiary_transaction_id")
    val beneficiaryTransactionId: Long,
    @Column(name = "terms_group_id")
    val termsGroupId: Long,
    @Column(name = "term_id")
    val termId: Long,
    @Column(name = "project_id")
    val projectId: Long,

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long? = null,

    var createdBy: Long? = null,
    var updatedBy: Long? = null,

    @Column(updatable = false)
    var createdAt: ZonedDateTime = ZonedDateTime.now(ZoneOffset.UTC),
    var updatedAt: ZonedDateTime = createdAt,
    var version: Long
) {
    @PreUpdate
    fun setLastUpdate() {
        version += 1
        updatedAt = ZonedDateTime.now(ZoneOffset.UTC)
    }
}