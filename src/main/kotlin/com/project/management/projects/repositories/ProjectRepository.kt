package com.project.management.projects.repositories

import com.project.management.projects.models.ProjectEntity
import com.project.management.projects.models.ProjectExpense
import com.project.management.projects.models.ProjectExpenseEntity
import com.project.management.projects.models.ProjectIncomeEntity
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface ProjectRepository : JpaRepository<ProjectEntity, Long> {

    fun findAllByOrganizationId(organizationId: Long): List<ProjectEntity>

    fun findByIdAndOrganizationId(id: Long, organizationId: Long): ProjectEntity?

    @Modifying
    @Query(
        value = "UPDATE projects SET deleted = NOW(), updated_by = :updatedBy WHERE id = :id AND organization_id = :organizationId AND deleted IS NULL",
        nativeQuery = true
    )
    fun deleteByIdAndOrganizationId(id: Long, organizationId: Long, updatedBy: Long)
}

@Repository
interface ProjectExpenseMutateRepository : JpaRepository<ProjectExpenseEntity, Long> {
    @Modifying
    @Query(
        value = "UPDATE project_expenses SET deleted = NOW(), updated_by = :updatedBy WHERE id = :id AND organization_id = :organizationId AND deleted IS NULL",
        nativeQuery = true
    )
    fun deleteByIdAndOrganizationId(id: Long, organizationId: Long, updatedBy: Long)
}

@Repository
interface ProjectExpenseQueryRepository : JpaRepository<ProjectExpense, Long> {
    fun findAllByOrganizationId(organizationId: Long): List<ProjectExpense>

    fun findAllByOrganizationIdAndProjectId(
        organizationId: Long,
        projectId: Long,
        pageable: Pageable
    ): Page<ProjectExpense>

    fun findAllByOrganizationIdAndBeneficiaryId(
        organizationId: Long,
        beneficiaryId: Long
    ): List<ProjectExpense>

    fun findByIdAndOrganizationId(id: Long, organizationId: Long): ProjectExpense?

    @Query(
        value = "SELECT * FROM project_expenses WHERE organization_id = :organizationId AND deleted IS NULL AND $createdAtLastWeek ORDER BY created_at DESC",
        nativeQuery = true
    )
    fun findAllByOrganizationIdCreatedAtLastWeek(organizationId: Long): List<ProjectExpense>

    @Query(
        value = "SELECT * FROM project_expenses WHERE organization_id = :organizationId AND deleted IS NULL AND $createdThisWeek ORDER BY created_at DESC",
        nativeQuery = true
    )
    fun findAllByOrganizationIdCreatedThisWeek(organizationId: Long): List<ProjectExpense>
}

@Repository
interface ProjectIncomeRepository : JpaRepository<ProjectIncomeEntity, Long> {
    fun findAllByOrganizationId(organizationId: Long): List<ProjectIncomeEntity>

    fun findAllByOrganizationIdAndProjectId(
        organizationId: Long,
        projectId: Long
    ): List<ProjectIncomeEntity>

    fun findAllByOrganizationIdAndCustomerId(
        organizationId: Long,
        customerId: Long
    ): List<ProjectIncomeEntity>

    fun findByIdAndOrganizationId(id: Long, organizationId: Long): ProjectIncomeEntity?

    @Modifying
    @Query(
        value = "UPDATE project_incomes SET deleted = NOW(), updated_by = :updatedBy WHERE id = :id AND organization_id = :organizationId AND deleted IS NULL",
        nativeQuery = true
    )
    fun deleteByIdAndOrganizationId(id: Long, organizationId: Long, updatedBy: Long)

    @Query(
        value = "SELECT * FROM project_incomes WHERE organization_id = :organizationId AND deleted IS NULL AND $createdAtLastWeek ORDER BY created_at DESC",
        nativeQuery = true
    )
    fun findAllByOrganizationIdCreatedAtLastWeek(organizationId: Long): List<ProjectIncomeEntity>

    @Query(
        value = "SELECT * FROM project_incomes WHERE organization_id = :organizationId AND deleted IS NULL AND $createdThisWeek ORDER BY created_at DESC",
        nativeQuery = true
    )
    fun findAllByOrganizationIdCreatedThisWeek(organizationId: Long): List<ProjectIncomeEntity>
}

internal const val createdAtLastWeek = "(created_at >= date_trunc('week', CURRENT_TIMESTAMP + interval '2 day' - interval '1 week')  - interval '2 day' and " +
        "created_at < date_trunc('week', CURRENT_TIMESTAMP + interval '2 day' ) - interval '2 day')"
internal const val createdThisWeek = "(created_at >= date_trunc('week', CURRENT_TIMESTAMP + interval '2 day') - interval '2 day')"