package com.project.management.projects.services

import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.projects.models.ProjectExpense
import com.project.management.beneficiaries.mappers.BeneficiaryMapper
import com.project.management.beneficiaries.services.BeneficiaryTransactionQueryService
import com.project.management.projects.mappers.ProjectExpenseMapper
import com.project.management.terms.mappers.TermMapper
import com.project.management.terms.mappers.TermsGroupMapper
import com.project.management.beneficiaries.validators.BeneficiaryValidator
import com.project.management.users.services.UsersService
import com.project.management.projects.validators.ProjectExpenseValidator
import com.project.management.projects.validators.ProjectValidator
import com.project.management.terms.validators.TermValidator
import com.project.management.terms.validators.TermsGroupValidator
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service

@Service
class ProjectExpensesQueryService(
    private val currentUser: CurrentUserConfig,
    private val projectExpenseMapper: ProjectExpenseMapper,
    private val termsGroupMapper: TermsGroupMapper,
    private val termMapper: TermMapper,
    private val beneficiaryMapper: BeneficiaryMapper,
    private val projectValidator: ProjectValidator,
    private val termsGroupValidator: TermsGroupValidator,
    private val termValidator: TermValidator,
    private val beneficiaryValidator: BeneficiaryValidator,
    private val projectExpenseValidator: ProjectExpenseValidator,
    private val beneficiaryTransactionQueryService: BeneficiaryTransactionQueryService,
    private val projectService: ProjectService,
    private val usersService: UsersService,
) {

    fun getAll(): List<ProjectExpense> {
        val user = currentUser.getCurrentUser()
        val projectExpenses = projectExpenseValidator.findAllByOrganizationId(user.organizationId)
        return projectExpenses
    }

    fun getAllByProjectId(projectId: Long, pageable: Pageable): List<ProjectExpense> {
        val user = currentUser.getCurrentUser()
        val project = projectValidator.validateExistsByIdAndOrganizationId(
            projectId = projectId,
            organizationId = user.organizationId
        )
        val projectExpenses = projectExpenseValidator.findAllByOrganizationIdAndProjectId(
            user.organizationId,
            projectId,
            pageable = pageable
        )

        return projectExpenses.content
    }

    fun getAllByBeneficiaryId(beneficiaryId: Long): List<ProjectExpense> {
        val user = currentUser.getCurrentUser()
        val beneficiary = beneficiaryValidator.validateBeneficiaryExistsByIdAndOrganizationId(
            beneficiaryId = beneficiaryId,
            organizationId = user.organizationId
        )
        val projectExpenses = projectExpenseValidator.findAllByOrganizationIdAndBeneficiaryId(
            organizationId = user.organizationId,
            beneficiaryId = beneficiaryId
        )
        return projectExpenses
    }
}