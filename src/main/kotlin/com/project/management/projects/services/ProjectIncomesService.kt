package com.project.management.projects.services

import com.project.management.common.configuration.CurrentUserConfig
import com.project.management.common.exceptions.BusinessException
import com.project.management.projects.requests.PostRequestProjectIncome
import com.project.management.projects.models.ProjectIncomeEntity
import com.project.management.projects.requests.PatchRequestProjectIncomeAmount
import com.project.management.customers.mappers.CustomerMapper
import com.project.management.customers.services.CustomerTransactionService
import com.project.management.projects.mappers.ProjectIncomeMapper
import com.project.management.projects.repositories.ProjectIncomeRepository
import com.project.management.customers.validators.CustomerValidator
import com.project.management.projects.validators.ProjectIncomeValidator
import com.project.management.projects.validators.ProjectValidator
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class ProjectIncomesService(
    private val projectIncomeRepository: ProjectIncomeRepository,
    private val currentUser: CurrentUserConfig,
    private val projectIncomeMapper: ProjectIncomeMapper,
    private val customerMapper: CustomerMapper,
    private val projectValidator: ProjectValidator,
    private val customerValidator: CustomerValidator,
    private val projectIncomeValidator: ProjectIncomeValidator,
    private val customerTransactionService: CustomerTransactionService,
    private val projectService: ProjectService,
) {

    fun getAll(): List<ProjectIncomeEntity> {
        val user = currentUser.getCurrentUser()
        val projectIncomes = projectIncomeRepository.findAllByOrganizationId(user.organizationId)
        return projectIncomes
    }

    fun getAllByProjectId(projectId: Long): List<ProjectIncomeEntity> {
        val user = currentUser.getCurrentUser()
        val project = projectValidator.validateExistsByIdAndOrganizationId(
            projectId = projectId,
            organizationId = user.organizationId
        )
        val projectIncomes = projectIncomeRepository.findAllByOrganizationIdAndProjectId(
            user.organizationId,
            projectId
        )
        return projectIncomes
    }

    fun getAllIncomesByCustomerId(customerId: Long): List<ProjectIncomeEntity> {
        val user = currentUser.getCurrentUser()
        val customer = customerValidator.validateCustomerExistsByIdAndOrganizationId(
            customerId = customerId,
            organizationId = user.organizationId
        )
        val projectIncomes = projectIncomeRepository.findAllByOrganizationIdAndCustomerId(
            organizationId = user.organizationId,
            customerId = customerId
        )
        return projectIncomes
    }

    @Transactional
    fun create(
        projectIncomeRequestDto: PostRequestProjectIncome,
        projectId: Long
    ): ProjectIncomeEntity {
        val user = currentUser.getCurrentUser()
        val project = projectValidator.validateExistsByIdAndOrganizationId(
            projectId = projectId,
            organizationId = user.organizationId
        )

        val customer = customerValidator.validateCustomerExistsByIdAndOrganizationId(
            projectIncomeRequestDto.customerId,
            user.organizationId
        )
        val transaction = customerTransactionService.create(
            transaction = projectIncomeMapper
                .toCustomerTransactionRequestDto(projectIncomeRequestDto, projectId),
            customerId = projectIncomeRequestDto.customerId
        )

        val projectExpense = projectIncomeMapper.toProjectIncome(
            projectIncomeRequestDto = projectIncomeRequestDto,
            customerTransactionId = transaction.id!!,
            customer = customer,
            organizationId = user.organizationId,
            projectId = project.id!!,
            userId = user.id,
            customerTransaction = transaction,
            project = project,
            version = 1
        )
        val result = projectIncomeRepository.save(projectExpense)

        projectService.modifyIncomes(
            projectId = projectId,
            amount = projectIncomeRequestDto.amount.toBigDecimal(),
            paid = projectIncomeRequestDto.amountPaid.toBigDecimal(),
        )

        return result
    }


    @Transactional
    fun updateAmount(
        request: PatchRequestProjectIncomeAmount,
        projectIncomeId: Long
    ): ProjectIncomeEntity {
        val loggedInUser = currentUser.getCurrentUser()
        val income = projectIncomeValidator.validateExistsByIdAndOrganization(
            projectIncomeId = projectIncomeId,
            organizationId = loggedInUser.organizationId
        )
        val incomeAmount = income.customerTransaction.amount
        val incomePaidAmount = income.customerTransaction.amountPaid
        if (!loggedInUser.isAdmin) {
            throw BusinessException.ForbiddenException(message = "You do not have permission to update this transaction.")
        }

        if (income.customerTransaction.version != request.transactionVersion) throw BusinessException.ConflictException()

        if (request.amountPaid > request.amount) {
            throw BusinessException.BadRequestException(
                message = "Amount paid cannot be greater than amount."
            )
        }

        customerTransactionService.updateAmount(
            request = request,
            customerTransactionId = income.customerTransaction.id!!
        )

        projectService.modifyIncomes(
            projectId = income.project.id!!,
            amount = incomeAmount.negate() + request.amount.toBigDecimal(),
            paid = incomePaidAmount.negate() + request.amountPaid.toBigDecimal(),
        )

        return projectIncomeValidator.validateExistsByIdAndOrganization(
            projectIncomeId = projectIncomeId,
            organizationId = loggedInUser.organizationId
        )
    }

    @Transactional
    fun delete(projectIncomeId: Long) {
        val loggedInUser = currentUser.getCurrentUser()
        val income = projectIncomeValidator.validateExistsByIdAndOrganization(
            projectIncomeId = projectIncomeId,
            organizationId = loggedInUser.organizationId
        )

        customerTransactionService.delete(income.customerTransaction.id!!)

        projectIncomeRepository.deleteByIdAndOrganizationId(
            id = projectIncomeId,
            organizationId = loggedInUser.organizationId,
            updatedBy = loggedInUser.id!!
        )

        projectService.modifyIncomes(
            projectId = income.project.id!!,
            amount = income.customerTransaction.amount.negate(),
            paid = income.customerTransaction.amountPaid.negate(),
        )
    }
}